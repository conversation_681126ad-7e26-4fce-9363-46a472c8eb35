"""
Utility functions for the PYQs Admin module.
"""

import os
import logging
import tempfile
from typing import Optional, Tuple
import fitz  # PyMuPDF

import utils.s3_utils
from utils.s3_utils import run_sudo_command
from utils.pdf_extraction_utils import extract_html_from_pdf, extract_questions_from_pdf
import config

logger = logging.getLogger(__name__)

def upload_question_paper_to_s3(
    file_content: bytes,
    exam_id: int,
    year: int,
    month: Optional[str] = None,
    shift: Optional[str] = None
) -> Tuple[bool, str]:
    """
    Upload a question paper PDF to S3.

    Args:
        file_content (bytes): The content of the PDF file
        exam_id (int): The ID of the exam
        year (int): The year of the exam
        month (Optional[str]): The month of the exam
        shift (Optional[str]): The shift of the exam

    Returns:
        Tuple[bool, str]: (success, s3_path)
    """
    try:
        # Create a temporary file to store the PDF
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        # Construct the S3 path
        file_name = f"{year}"
        if month:
            file_name += f"_{month}"
        if shift:
            file_name += f"_{shift}"
        file_name += ".pdf"

        s3_dir = f"/supload/questionPapers/{exam_id}"
        s3_path = f"{s3_dir}/{file_name}"

        # Get the full S3 paths
        full_s3_dir = utils.s3_utils.get_s3_path(s3_dir)
        full_s3_path = utils.s3_utils.get_s3_path(s3_path)

        print(f"Full S3 directory path: {full_s3_dir}")
        print(f"Full S3 file path: {full_s3_path}")

        logger.info(f"Uploading question paper to S3: {full_s3_path}")

        # Create the directory if it doesn't exist
        success, message = run_sudo_command(
            ["sudo", "mkdir", "-p", full_s3_dir],
            f"Failed to create directory {full_s3_dir}"
        )

        if not success:
            logger.error(f"Failed to create directory: {message}")
            return False, ""

        # Copy the file to S3 mount path using sudo
        success, message = run_sudo_command(
            ["sudo", "cp", temp_file_path, full_s3_path],
            f"Failed to copy file to {full_s3_path}"
        )

        # Clean up the temporary file
        try:
            os.unlink(temp_file_path)
        except Exception as e:
            logger.warning(f"Failed to delete temporary file: {e}")

        if not success:
            logger.error(f"Failed to upload file: {message}")
            return False, ""

        return True, s3_path
    except Exception as e:
        logger.error(f"Error uploading question paper to S3: {e}")
        return False, ""

def extract_text_from_pdf(pdf_path: str) -> Optional[str]:
    """
    Extract text from a PDF file.

    Args:
        pdf_path (str): The path to the PDF file in S3

    Returns:
        Optional[str]: The extracted text, or None if extraction failed
    """
    try:
        # Get the full S3 path
        full_s3_path = utils.s3_utils.get_s3_path(pdf_path)
        logger.info(f"Extracting text from PDF: {full_s3_path}")

        # Open the PDF file
        pdf_document = fitz.open(full_s3_path)

        text = ""
        for page_num in range(pdf_document.page_count):
            page = pdf_document[page_num]
            text += page.get_text()

        pdf_document.close()
        logger.info(f"Successfully extracted text from PDF: {len(text)} characters")

        return text
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {e}")
        return None

# Note: extract_html_from_pdf and extract_questions_from_pdf are now imported from utils.pdf_extraction_utils
